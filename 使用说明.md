# Radiance Cascades 2D 使用说明

## 🎉 成功实现！

恭喜！你现在拥有了一个完整的**Radiance Cascades 2D**实现！

## 🚀 如何运行

1. **编译项目**：
   ```bash
   dotnet build
   ```

2. **运行程序**：
   ```bash
   dotnet run --project Game
   ```

3. **选择运行模式**：
   - 输入 `1` 选择简化版（推荐，稳定运行）
   - 输入 `2` 选择完整版（包含完整的级联算法）

## 🎮 控制说明

### 简化版控制
- **鼠标左键拖拽**：移动光源位置
- **↑ 键**：增加光照强度
- **↓ 键**：减少光照强度
- **ESC 键**：退出程序

### 完整版控制（如果选择模式2）
- **鼠标左键**：移动光源位置
- **SPACE 键**：显示/隐藏探针位置
- **R 键**：开关全局光照显示
- **C 键**：切换级联视图
- **↑↓ 键**：调整光照强度
- **1-5 数字键**：改变光源颜色（红、绿、蓝、黄、白）

## 🔍 你将看到什么

### 简化版效果
- 一个黄色光源（可以用鼠标移动）
- 三个灰色几何体（圆形和矩形）
- 实时的全局光照效果，包括：
  - 光线衰减
  - 阴影投射
  - 间接光照

### 完整版效果
- 多级联的Radiance Cascades算法
- 可视化的探针网格
- 更复杂的光照计算
- 调试信息显示

## 📚 技术原理回顾

### Radiance Cascades核心概念

1. **半影假设**：
   - 距离光源越远 → 需要更多角度分辨率
   - 距离光源越近 → 需要更多空间分辨率

2. **级联结构**：
   - Cascade 0：最多探针，最少光线方向
   - Cascade 1：1/4探针，4倍光线方向
   - Cascade 2：1/16探针，16倍光线方向
   - ...

3. **算法流程**：
   ```
   光线投射 → 级联合并 → 最终渲染
   ```

### 实现特点

- **SDF场景表示**：使用数学函数定义几何体
- **光线投射优化**：SDF加速的自适应步进
- **级联合并**：双线性插值处理不同分辨率
- **实时渲染**：CPU实现，60FPS目标

## 🛠️ 代码结构

```
Game/
├── Program.cs              # 主程序和完整版实现
├── SimpleRadianceCascades.cs  # 简化版实现
├── lighting.vs            # 顶点着色器（原有）
└── lighting.fs            # 片段着色器（原有）
```

### 关键类说明

- `RadianceProbe`：辐射探针，存储位置和各方向辐射度
- `RadianceCascade`：级联结构，管理探针网格
- `RadianceCascades2D`：完整系统管理类
- `SimpleRadianceCascades`：简化版演示类

## 🎯 学习要点

通过这个实现，你学到了：

1. **全局光照算法**：理解了现代实时GI的工作原理
2. **SDF技术**：学会了用数学函数表示几何体
3. **空间数据结构**：掌握了多分辨率网格的概念
4. **光线投射**：实现了高效的光线-场景交互
5. **实时渲染**：体验了CPU端的实时图形算法

## 🚀 扩展建议

如果你想进一步改进：

### 性能优化
- 使用GPU计算着色器重写
- 实现多线程CPU版本
- 优化数据结构和内存布局

### 视觉改进
- 添加天空盒积分
- 实现体积光效果
- 改进阴影质量和软阴影

### 功能扩展
- 支持动态几何体
- 添加多光源支持
- 实现3D版本
- 集成到游戏引擎

## 📖 参考资料

- [Alexander Sannikov原始论文](https://github.com/Raikiri/RadianceCascadesPaper)
- [GM Shaders教程系列](https://mini.gmshaders.com/p/radiance-cascades)
- [MΛX基础教程](https://m4xc.dev/articles/fundamental-rc/)

## 🎊 总结

你现在拥有了一个功能完整的Radiance Cascades 2D实现！这不仅是一个技术演示，更是对现代实时全局光照算法的深入理解。

**享受你的光照之旅！** ✨

---

*如果遇到任何问题，请检查控制台输出获取调试信息。*
